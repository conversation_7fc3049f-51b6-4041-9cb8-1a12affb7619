import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'providers/auth_provider.dart';
import 'providers/employee_provider.dart';
import 'utils/app_colors.dart';
import 'utils/app_router.dart';

void main() {
  runApp(const EmployeeApp());
}

class EmployeeApp extends StatelessWidget {
  const EmployeeApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        ChangeNotifierProvider(create: (_) => EmployeeProvider()),
      ],
      child: Consumer<AuthProvider>(
        builder: (context, authProvider, _) {
          return MaterialApp.router(
            title: 'نظام إدارة الموظفين',
            theme: AppTheme.lightTheme,
            routerConfig: AppRouter.createRouter(),
            debugShowCheckedModeBanner: false,
            builder: (context, child) {
              // Initialize auth status on app start
              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (!authProvider.isLoading) {
                  authProvider.checkAuthStatus();
                }
              });
              return child!;
            },
          );
        },
      ),
    );
  }
}
