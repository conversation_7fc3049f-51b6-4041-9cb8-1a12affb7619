import 'package:shared_preferences/shared_preferences.dart';
import '../models/employee.dart';
import 'mock_data_service.dart';

class AuthService {
  static const String _isLoggedInKey = 'isLoggedIn';
  static const String _currentUserIdKey = 'currentUserId';

  // Mock credentials for demo
  static const Map<String, String> _mockCredentials = {
    'admin': 'admin123',
    'ahmed.ali': 'password123',
    'sara.mohamed': 'password123',
    'omar.hassan': 'password123',
  };

  Future<bool> login(String username, String password) async {
    // Check mock credentials
    if (_mockCredentials.containsKey(username) && 
        _mockCredentials[username] == password) {
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_isLoggedInKey, true);
      await prefs.setString(_currentUserIdKey, username);
      
      return true;
    }
    return false;
  }

  Future<void> logout() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_isLoggedInKey);
    await prefs.remove(_currentUserIdKey);
  }

  Future<bool> isLoggedIn() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_isLoggedInKey) ?? false;
  }

  Future<String?> getCurrentUserId() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_currentUserIdKey);
  }

  Future<Employee?> getCurrentUser() async {
    final userId = await getCurrentUserId();
    if (userId != null) {
      return MockDataService.getEmployeeById(userId);
    }
    return null;
  }

  List<String> getAvailableUsernames() {
    return _mockCredentials.keys.toList();
  }
}
